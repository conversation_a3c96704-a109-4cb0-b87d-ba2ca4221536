import { Configuration, App } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import * as info from '@midwayjs/info';
import { join } from 'path';
import { DefaultErrorFilter } from './filter/default.filter';
import * as axios from '@midwayjs/axios'; // 必须引入axios组件
import * as jwt from '@midwayjs/jwt'; // 必须引入@midwayjs/jwt
import { OperationLogMiddleware } from './middleware/operation-log.middleware';
import { RequestLoggerMiddleware } from './middleware/requestlogger.middleware';
import { FormatMiddleware } from './middleware/format.middleware';
// 导入JWT认证组件
import jwtComponent from './component/jwt-component';
// 导入JWT认证中间件
import { JwtAuthMiddleware } from './component/jwt/middleware/jwt.middleware';
import * as sequelize from '@midwayjs/sequelize';

@Configuration({
  imports: [
    koa,
    validate,
    jwt, // 必须引入@midwayjs/jwt
    axios, // 必须引入@midwayjs/axios
    jwtComponent, // 引入JWT认证组件
    sequelize,
    {
      component: info,
      enabledEnvironment: ['local'],
    },
  ],
  importConfigs: [join(__dirname, './config')],
})
export class MainConfiguration {
  @App('koa')
  app: koa.Application;

  async onReady() {
    // add middleware
    // 注意：JWT认证中间件必须在其他业务中间件之前执行
    this.app.useMiddleware([
      JwtAuthMiddleware,        // JWT认证中间件，必须放在最前面
      RequestLoggerMiddleware,
      OperationLogMiddleware,
      FormatMiddleware,
    ]);
    // add filter
    this.app.useFilter([DefaultErrorFilter]);
  }
}
