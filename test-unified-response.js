const jwt = require('jsonwebtoken');

// 使用与配置文件中相同的secret
const secret = 'question-secret';

// 创建一个有效的JWT token
const payload = {
  userId: 'test-user-unified',
  username: 'testuser-unified',
  roles: ['user'],
  type: 'access',
  userCode: 'test-unified',
  enterpriseCode: 'test-enterprise',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (2 * 60 * 60) // 2小时后过期
};

const token = jwt.sign(payload, secret);

console.log('统一响应格式测试Token:');
console.log(token);
