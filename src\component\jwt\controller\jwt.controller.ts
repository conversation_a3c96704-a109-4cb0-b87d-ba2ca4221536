/*
 * @Description: JWT认证控制器
 * @Date: 2025-05-15
 */
import { Body, Config, Controller, Get, Inject, Post } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { JwtAuthService } from '../service/jwt.service';
import { JwtAuthApiService } from '../service/auth.service';
import { CustomError } from '../../../error/custom.error';
import { JwtAuthConfig } from '../interface';
import { ApiResponseUtil } from '../../../common/ApiResponse';

@Controller('/auth')
export class JwtAuthController {
  @Inject()
  ctx: Context;

  @Inject()
  jwtAuthService: JwtAuthService;

  @Inject()
  jwtAuthApiService: JwtAuthApiService;

  @Config('jwtAuth')
  jwtAuthConfig: JwtAuthConfig;

  /**
   * 用户登录
   * @param body 登录参数
   */
  @Post('/login')
  async login(@Body() body: { code: string }) {
    const { code } = body;
    if (!code) {
      throw new CustomError('未收到需要解码的值', 400);
    }
    const info = await this.jwtAuthApiService.getDataConver(code);

    const { enterpriseCode, userCode, ...others } = info;

    if (!enterpriseCode || !userCode) {
      throw new CustomError('企业编码和用户编码不能为空', 400);
    }

    const result = await this.jwtAuthService.login({
      enterpriseCode,
      userCode,
    });
    return ApiResponseUtil.success({
      ...result,
      params: others,
    }, '登录成功');
  }

  /**
   * 刷新令牌
   * @param body 刷新令牌参数
   */
  @Post('/refresh-token')
  async refreshToken(@Body('refreshToken') refreshToken: string) {
    if (!refreshToken) {
      throw new CustomError('刷新令牌不能为空', 400);
    }

    const result = await this.jwtAuthService.refreshToken({ refreshToken });
    return ApiResponseUtil.success(result, '令牌刷新成功');
  }

  /**
   * 获取当前用户信息
   */
  @Get('/current-user')
  async getCurrentUser() {
    const result = await this.jwtAuthService.getCurrentUser();
    return ApiResponseUtil.success(result, '获取用户信息成功');
  }

  /**
   * 退出登录
   */
  @Post('/logout')
  async logout() {
    await this.jwtAuthService.logout();
    return ApiResponseUtil.success({ success: true }, '退出登录成功');
  }
}
