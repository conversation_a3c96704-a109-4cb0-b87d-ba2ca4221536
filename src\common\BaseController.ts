import { Inject } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { AuthError } from '../error/custom.error';

/**
 * 基础Controller类，提供通用方法
 */
export abstract class BaseController {
  @Inject()
  ctx: Context;

  /**
   * 获取当前登录用户信息
   */
  protected getCurrentUser(): { userId: string; userName: string } {
    console.log('this.ctx.state', this.ctx.state);
    const userInfo = this.ctx.state.user || {};
    const userId = userInfo.userId || userInfo.id;
    const userName = userInfo.userName || userInfo.name;

    if (!userId) {
      throw new AuthError('用户未登录或token无效');
    }

    return { userId, userName };
  }

  /**
   * 获取当前用户ID
   */
  protected getCurrentUserId(): string {
    return this.getCurrentUser().userId;
  }

  /**
   * 获取当前用户名
   */
  protected getCurrentUserName(): string {
    return this.getCurrentUser().userName;
  }

  /**
   * 记录操作日志
   */
  protected logOperation(operation: string, details?: any) {
    this.ctx.logger.info(`用户操作: ${operation}`, {
      userId: this.getCurrentUserId(),
      operation,
      details,
      timestamp: new Date().toISOString(),
    });
  }
}
