import { Catch } from '@midwayjs/core';
import { Context } from '@midwayjs/koa';
import { ApiResponseUtil } from '../common/ApiResponse';
import { ErrorCode } from '../common/ErrorCode';
import { BusinessError } from '../error/custom.error';

@Catch()
export class DefaultErrorFilter {
  async catch(err: Error, ctx: Context) {
    // 记录错误日志
    ctx.logger.error('捕获到异常:', err);

    // 如果是业务异常，使用业务异常的错误码和消息
    if (err instanceof BusinessError) {
      // 根据错误码设置HTTP状态码
      ctx.status = this.getHttpStatusFromErrorCode(err.errCode);
      return ApiResponseUtil.error(err.errCode, err.message);
    }

    // 参数校验错误
    if (err.name === 'ValidationError') {
      ctx.status = 422; // 设置HTTP状态码为422
      return ApiResponseUtil.error(
        ErrorCode.PARAM_ERROR,
        err.message || '参数校验失败'
      );
    }

    // JWT认证错误
    if (err.name === 'UnauthorizedError' || err.message.includes('jwt')) {
      ctx.status = 401; // 设置HTTP状态码为401
      return ApiResponseUtil.error(
        ErrorCode.AUTH_ERROR,
        '认证失败，请重新登录'
      );
    }

    // 404错误
    if (ctx.status === 404) {
      ctx.status = 404; // 确保HTTP状态码为404
      return ApiResponseUtil.error(ErrorCode.NOT_FOUND, '请求的资源不存在');
    }

    // 其他未知错误
    ctx.status = 500; // 设置HTTP状态码为500
    const isDev = ctx.app.getEnv() === 'local';
    return ApiResponseUtil.error(
      ErrorCode.SYS_ERROR,
      isDev ? err.message : '系统内部错误'
    );
  }

  /**
   * 根据错误码获取对应的HTTP状态码
   */
  private getHttpStatusFromErrorCode(errCode: number): number {
    switch (errCode) {
      case ErrorCode.OK:
        return 200;
      case ErrorCode.BIZ_ERROR:
        return 400;
      case ErrorCode.AUTH_ERROR:
        return 401;
      case ErrorCode.NOT_FOUND:
        return 404;
      case ErrorCode.TIME_OUT:
        return 408;
      case ErrorCode.PARAM_ERROR:
        return 422;
      case ErrorCode.SYS_ERROR:
        return 500;
      default:
        return 500; // 默认返回500
    }
  }
}
