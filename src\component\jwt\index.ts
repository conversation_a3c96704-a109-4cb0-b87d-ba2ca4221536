/*
 * @Description: JWT认证组件入口
 * @Date: 2025-05-15
 */
import { App, Configuration } from '@midwayjs/core';
import { Application } from '@midwayjs/koa';
import * as DefaultConfig from './config/config.default';

@Configuration({
  namespace: 'jwt-auth',
  importConfigs: [
    {
      default: DefaultConfig,
    },
  ],
  // 声明组件依赖
  imports: [
    '@midwayjs/jwt', // 依赖jwt组件
    '@midwayjs/axios', // 依赖axios组件
  ],
})
export class JwtAuthConfiguration {
  @App()
  app: Application;

  async onReady() {
    // 组件就绪
    // 动态配置API管理器的baseURL
    const jwtAuthConfig = this.app.getConfig('jwtAuth');
    if (jwtAuthConfig && jwtAuthConfig.apiManagerBaseURL) {
      const axiosConfig = this.app.getConfig('axios') || {};
      if (!axiosConfig.clients) {
        axiosConfig.clients = {};
      }
      if (!axiosConfig.clients.jwtApiManager) {
        axiosConfig.clients.jwtApiManager = {};
      }
      axiosConfig.clients.jwtApiManager.baseURL =
        jwtAuthConfig.apiManagerBaseURL;
      axiosConfig.clients.jwtApiManager.timeout =
        jwtAuthConfig.apiTimeout || 10000;
      this.app.addConfigObject({ axios: axiosConfig });
    }
  }
}

// 导出组件内容
export {
  JwtAuthConfig,
  JwtLoginParams,
  JwtLoginResult,
  JwtRefreshTokenParams,
  JwtRefreshTokenResult,
  JwtTokenPayload,
  JwtUserInfo,
} from './interface';
export { JwtAuthMiddleware } from './middleware/jwt.middleware';
export { JwtAuthService } from './service/jwt.service';
export { JwtAuthController } from './controller/jwt.controller';
export { JwtAuthApiService } from './service/auth.service';
export { JwtApiManager } from './common/api-manager';
export { JwtLogger } from './common/logger';
