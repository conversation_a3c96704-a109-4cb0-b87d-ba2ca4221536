/*
 * @Description: JWT认证组件入口
 * @Date: 2025-05-15
 */
import { App, Configuration } from '@midwayjs/core';
import { Application } from '@midwayjs/koa';
import * as DefaultConfig from './config/config.default';
import { JwtAuthMiddleware } from './middleware/jwt.middleware';
// 直接导入控制器以确保注册
import './controller/jwt.controller';

@Configuration({
  namespace: 'jwt-auth',
  importConfigs: [
    {
      default: DefaultConfig,
    },
  ],
  // 声明组件依赖
  imports: [
    '@midwayjs/jwt', // 依赖jwt组件
    '@midwayjs/axios', // 依赖axios组件
  ],
  // 扫描当前目录下的所有文件
  importObjects: {
    controller: './controller/**/*',
    service: './service/**/*',
    middleware: './middleware/**/*',
  },
})
export class JwtAuthConfiguration {
  @App()
  app: Application;

  async onReady() {
    // 组件就绪

    // 1. 自动注册JWT认证中间件（必须在最前面）
    const jwtAuthConfig = this.app.getConfig('jwtAuth');
    if (jwtAuthConfig && jwtAuthConfig.enable !== false) {
      console.log('JWT认证组件：自动注册JWT认证中间件');
      // 将JWT认证中间件添加到中间件列表的最前面
      this.app.useMiddleware([JwtAuthMiddleware]);
    }

    // 2. 动态配置API管理器的baseURL
    if (jwtAuthConfig && jwtAuthConfig.apiManagerBaseURL) {
      const axiosConfig = this.app.getConfig('axios') || {};
      if (!axiosConfig.clients) {
        axiosConfig.clients = {};
      }
      if (!axiosConfig.clients.jwtApiManager) {
        axiosConfig.clients.jwtApiManager = {};
      }
      axiosConfig.clients.jwtApiManager.baseURL =
        jwtAuthConfig.apiManagerBaseURL;
      axiosConfig.clients.jwtApiManager.timeout =
        jwtAuthConfig.apiTimeout || 10000;
      this.app.addConfigObject({ axios: axiosConfig });
    }
  }
}

// 导出组件内容
export {
  JwtAuthConfig,
  JwtLoginParams,
  JwtLoginResult,
  JwtRefreshTokenParams,
  JwtRefreshTokenResult,
  JwtTokenPayload,
  JwtUserInfo,
} from './interface';
export { JwtAuthMiddleware } from './middleware/jwt.middleware';
export { JwtAuthService } from './service/jwt.service';
export { JwtAuthController } from './controller/jwt.controller';
export { JwtAuthApiService } from './service/auth.service';
export { JwtApiManager } from './common/api-manager';
export { JwtLogger } from './common/logger';
